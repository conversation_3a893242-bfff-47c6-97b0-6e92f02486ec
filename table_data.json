﻿{
    "sheet_IqZBBqs7": {
        "uid": "sheet_IqZBBqs7",
        "name": "时空表格",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": true,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "alternateLevel": 0,
            "skipTop": false,
            "selectedCustomStyleKey": "",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1",
                    "replaceDivide": ""
                }
            }
        },
        "sourceData": {
            "note": "记录时空信息的表格，应保持在一行",
            "initNode": "本轮需要记录当前时间、地点、人物信息，使用insertRow函数",
            "deleteNode": "此表大于一行时应删除多余行",
            "updateNode": "当描写的场景，时间，人物变更时"
        },
        "content": [
            [
                null,
                "日期",
                "时间",
                "地点（当前描写）",
                "在场角色"
            ]
        ]
    },
    "sheet_8YaqKcTT": {
        "uid": "sheet_8YaqKcTT",
        "name": "角色特征表格",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": true,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "alternateLevel": 0,
            "skipTop": false,
            "selectedCustomStyleKey": "",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1",
                    "replaceDivide": ""
                }
            }
        },
        "sourceData": {
            "note": "角色天生或不易改变的特征csv表格，思考本轮有否有其中的角色，他应作出什么反应",
            "initNode": "本轮必须从上文寻找已知的所有角色使用insertRow插入，角色名不能为空",
            "deleteNode": "",
            "updateNode": "当角色的身体出现持久性变化时，例如伤痕/当角色有新的爱好，职业，喜欢的事物时/当角色更换住所时/当角色提到重要信息时",
            "insertNode": "当本轮出现表中没有的新角色时，应插入"
        },
        "content": [
            [
                null,
                "角色名",
                "身体特征",
                "性格",
                "职业",
                "爱好",
                "长期欲望",
                "场景目标",
                "喜欢的事物（作品、虚拟人物、物品等）",
                "秘密",
                "其他重要信息"
            ]
        ]
    },
    "sheet_Osg5W1Bs": {
        "uid": "sheet_Osg5W1Bs",
        "name": "关系矩阵",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": true,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "alternateLevel": 0,
            "skipTop": false,
            "selectedCustomStyleKey": "",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1",
                    "replaceDivide": ""
                }
            }
        },
        "sourceData": {
            "note": "定义角色之间的动态关系，支持NPC与NPC之间的互动。",
            "initNode": "本轮必须从上文寻找已知的所有角色使用insertRow插入，角色名不能为空",
            "deleteNode": "",
            "updateNode": "当角色和<user>的交互不再符合原有的记录时/当角色和<user>的关系改变时",
            "insertNode": "当本轮出现表中没有的新角色时，应插入"
        },
        "content": [
            [
                null,
                "源角色",
                "目标角色",
                "关系/态度",
                "关系描述",
                "影响关系的重大事件"
            ]
        ]
    },
    "sheet_CRKRl6sX": {
        "uid": "sheet_CRKRl6sX",
        "name": "任务、命令或者约定表格",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": false,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "alternateLevel": 0,
            "skipTop": false,
            "selectedCustomStyleKey": "",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1",
                    "replaceDivide": ""
                }
            }
        },
        "sourceData": {
            "note": "思考本轮是否应该执行任务/赴约",
            "deleteNode": "当大家赴约时/任务或命令完成时/任务，命令或约定被取消时",
            "updateNode": "",
            "insertNode": "当特定时间约定一起去做某事时/某角色收到做某事的命令或任务时"
        },
        "content": [
            [
                null,
                "角色",
                "任务",
                "地点",
                "持续时间",
                "动机"
            ]
        ]
    },
    "sheet_wZiXJrf7": {
        "uid": "sheet_wZiXJrf7",
        "name": "重要事件历史表格",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": true,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "alternateLevel": 0,
            "skipTop": false,
            "selectedCustomStyleKey": "",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1",
                    "replaceDivide": ""
                }
            }
        },
        "sourceData": {
            "note": "记录<user>或角色经历的重要事件",
            "initNode": "本轮必须从上文寻找可以插入的事件并使用insertRow插入",
            "deleteNode": "",
            "updateNode": "",
            "insertNode": "当某个角色经历让自己印象深刻的事件时，比如表白、分手等"
        },
        "content": [
            [
                null,
                "角色",
                "事件简述",
                "日期",
                "地点",
                "情绪"
            ]
        ]
    },
    "sheet_NAQ4rBUt": {
        "uid": "sheet_NAQ4rBUt",
        "name": "重要物品表格",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": false,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "alternateLevel": 0,
            "skipTop": false,
            "selectedCustomStyleKey": "",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1",
                    "replaceDivide": ""
                }
            }
        },
        "sourceData": {
            "note": "对某人很贵重或有特殊纪念意义的物品",
            "deleteNode": "",
            "updateNode": "",
            "insertNode": "当某人获得了贵重或有特殊意义的物品时/当某个已有物品有了特殊意义时"
        },
        "content": [
            [
                null,
                "拥有人",
                "物品描述",
                "物品名",
                "重要原因"
            ]
        ]
    },
    "sheet_1wJitbOH": {
        "uid": "sheet_1wJitbOH",
        "name": "场景实时状态",
        "domain": "chat",
        "type": "dynamic",
        "enable": true,
        "required": true,
        "tochat": true,
        "triggerSend": false,
        "triggerSendDeep": 1,
        "config": {
            "toChat": true,
            "useCustomStyle": false,
            "triggerSendToChat": false,
            "alternateTable": false,
            "insertTable": false,
            "skipTop": false,
            "alternateLevel": "0",
            "selectedCustomStyleKey": "自定义样式",
            "customStyles": {
                "自定义样式": {
                    "mode": "regex",
                    "basedOn": "html",
                    "regex": "/(^[\\s\\S]*$)/g",
                    "replace": "$1"
                }
            }
        },
        "sourceData": {
            "note": "捕捉场景发生时，角色的实时动态信息, 角色两两之间的相对位置关系"
        },
        "content": [
            [
                null,
                "角色A姓名",
                "角色B姓名",
                "两人相对位置关系"
            ]
        ]
    },
    "mate": {
        "type": "chatSheets",
        "version": 1
    }
}