/```GlobalStatus\s*时间:\s*([\s\S]*?)(?=\s*场景:)\s*场景:\s*([\s\S]*?)\s*UserInScene:\s*(true|false)\s*场景内NPC:\s*([\s\S]*?)(?=\s*附近可互动物品:)\s*附近可互动物品:\s*([\s\S]*?)(?=\s*附近可前往地点:)\s*附近可前往地点:\s*([\s\S]*?)(?=\s*未来事件:)\s*未来事件:\s*([\s\S]*?)```/g

```GlobalStatus
时间: 1941年/11月/02日 秋季 星期日 晚上 20:55
场景: 潮湿的城堡石室, 窗外暴雨, 室内壁炉燃烧
UserInScene: false # 布尔值，用于判断 {{user}} 是否在当前场景
场景内NPC:
- NPC_A: [意图] - [位置]
- NPC_B: [意图] - [位置]
附近可互动物品:
- [物品名称] - [状态]
附近可前往地点:
- [地点描述] ([距离或状态])
未来事件:
- [事件描述]
```


```
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏状态</title>
    <!-- 引入Tailwind CSS以实现快速、现代化的样式 -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- 引入Inter字体，提升文本的可读性和美观度 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <style>
        /* 自定义基础样式 */
        body {
            font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* 自定义滚动条样式，使其更美观且与暗色主题协调 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: #2d3748; /* 暗色背景 */
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #718096; /* 滚动条滑块颜色 */
            border-radius: 3px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #a0aec0; /* 鼠标悬停时颜色 */
        }
        
        /* 兼容Firefox的滚动条样式 */
        .custom-scrollbar {
            scrollbar-width: thin;
            scrollbar-color: #718096 #2d3748;
        }
    </style>
</head>

<!-- 页面主体，使用深灰色背景 -->
<body class="bg-gray-900 flex justify-center items-start p-3 md:p-6 text-sm text-gray-300">

    <!-- 主容器，采用较浅的深色背景、圆角和边框，营造出层次感 -->
    <div class="status-container bg-gray-800 rounded-xl shadow-lg border border-gray-700 p-4 md:p-6 w-full max-w-5xl">
        
        <!-- 顶部信息区：使用flex布局，在中等屏幕及以上确保内容在一行内显示 -->
        <div class="top-info-flex flex flex-wrap md:flex-nowrap gap-4 md:gap-6 items-center mb-6">
            
            <!-- 时间信息块 -->
            <div class="info-block flex items-baseline whitespace-nowrap">
                <span class="info-label font-medium text-gray-400 mr-2">时间:</span>
                <span class="info-value text-gray-100 font-semibold">$1</span>
            </div>
            
            <!-- 场景信息块，允许其占据更多空间 -->
            <div class="info-block flex items-baseline flex-grow min-w-0">
                <span class="info-label font-medium text-gray-400 mr-2">场景:</span>
                <!-- 使用truncate确保长文本不会破坏布局 -->
                <span class="info-value text-gray-100 truncate" title="$2">$2</span>
            </div>

            <!-- 场景内用户状态 -->
            <div class="info-block flex items-baseline whitespace-nowrap">
                <span class="info-label font-medium text-gray-400 mr-2">UserInScene:</span>
                <span class="info-value text-green-400 font-semibold">$3</span>
            </div>
        </div>

        <!-- 主要内容网格：响应式布局，根据屏幕宽度自动调整列数 -->
        <div class="content-grid grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            
            <!-- 带有不同颜色左边框的区块，用于区分不同类别的内容 -->
            <div class="section bg-gray-700/50 rounded-lg p-4 border-l-4 border-blue-500 transition-all hover:bg-gray-700">
                <h3 class="section-title font-semibold text-gray-200 mb-2">场景内NPC人物</h3>
                <div class="section-content custom-scrollbar text-gray-300 text-xs leading-relaxed whitespace-pre-wrap max-h-36 overflow-y-auto">$4</div>
            </div>
            
            <div class="section bg-gray-700/50 rounded-lg p-4 border-l-4 border-green-500 transition-all hover:bg-gray-700">
                <h3 class="section-title font-semibold text-gray-200 mb-2">附近可互动物品</h3>
                <div class="section-content custom-scrollbar text-gray-300 text-xs leading-relaxed whitespace-pre-wrap max-h-36 overflow-y-auto">$5</div>
            </div>
            
            <div class="section bg-gray-700/50 rounded-lg p-4 border-l-4 border-yellow-500 transition-all hover:bg-gray-700">
                <h3 class="section-title font-semibold text-gray-200 mb-2">附近可前往地点</h3>
                <div class="section-content custom-scrollbar text-gray-300 text-xs leading-relaxed whitespace-pre-wrap max-h-36 overflow-y-auto">$6</div>
            </div>
            
            <div class="section bg-gray-700/50 rounded-lg p-4 border-l-4 border-red-500 transition-all hover:bg-gray-700">
                <h3 class="section-title font-semibold text-gray-200 mb-2">未来事件</h3>
                <div class="section-content custom-scrollbar text-gray-300 text-xs leading-relaxed whitespace-pre-wrap max-h-36 overflow-y-auto">$7</div>
            </div>
        </div>
    </div>

</body>
</html>
```
