# 角色卡生成

## 任务说明

目标:
根据用户提供的简短材料（核心种子）或纯粹的核心构想，通过引导式自由联想、发散性思考与结构化整合，从无到有地设计并生成一份结构严谨、心理深度足够、细节生动且支持动态模拟的角色卡。此任务旨在激发创造力，帮助构建角色的核心概念、内在动力、关键特质网络、具体表现、关键经历和人际风格，确保最终产出的角色卡逻辑自洽、信息聚焦、符合设计意图，并能作为动态角色扮演模拟的基础。

所需输入:

1. 核心种子 (Core Seed): 用户提供的起点。这可以非常简洁，例如：
       一句话描述: "一个总是笑嘻嘻但眼神偶尔会变得非常冰冷的老好人。"
       期望感觉/效果: "我想创造一个让人感觉既可靠又危险的角色。"
       核心矛盾: "责任感 vs. 对自由的极端渴望。"
       一个关键意象或象征: "像一座内部即将喷发的休眠火山。"
       角色功能/主题: "体现'压抑久了必然扭曲'的主题。"
       甚至只有一个名字和一个模糊的印象。
2. 可选: 用户可能提供的任何已有的零散想法或片段。

## 思考内容

核心理念:

1. 效果驱动: 设计始于“我希望这个角色给人带来______的感觉/思考？”或“这个角色需要体现______的主题/冲突？”
2. 联想与构建: 从核心“种子”出发，通过自由联想扩展意象和概念，然后结构化地构建角色的各个层面。
3. 设计而非发现: 主动设计其心理结构和背景故事，使其服务于核心目标。
4. 深度与一致性: 借鉴心理学常识创造可信的内在逻辑，确保角色的表象与设计的深层内核一致。
5. 聚焦核心: 优先设计和打磨最能体现角色核心概念的关键元素（如1-2个核心冲突、3-5个关键特质），避免无关细节干扰。
6. 动态与互动: 主动设计特质间的关键互动关系（激活、抑制、冲突），为角色赋予动态反应能力。
7. 表现力与细节: 精心设计能反映内在状态的、具体的、尤其是细微的外显信号（生理、声音、姿态、小动作），增强生动性。
8. 迭代与修正: 创作过程是动态的，允许在构建过程中调整和优化想法，以达到最佳效果。

## 输出结构

输出要求:

1. 语言: 使用中文输出。
2. 格式: 严格使用 YAML 格式。严禁在 YAML 值中包含 Markdown 的加粗（如 `text`）或其他非 YAML 标准格式。
3. 结构: 生成的角色卡必须包含下方“目标结构模板”中的所有结构层级和信息字段（除非某可选条目根据设计意图被移除），且顺序一致。
4. 无指导标记: 生成的 YAML 文件中，严禁包含任何指导性标记，如 `# (可选)` 或括号内的描述性提示。这些提示应体现在实际内容中，最终输出应是纯粹的结构化数据。
5. 内容核心: 所有内容都应围绕“核心种子”进行联想、设计和构建，并服务于最初的“效果驱动”目标。
6. 逻辑自洽: 确保设计出的各个部分能够相互印证，形成可信的内部逻辑。可在描述中简要说明设计的内在联系。
7. 表现力优先: 优先设计和包含能生动体现角色核心特质和内在状态的表现元素（特别是细微信号）。
8. 遵循设计步骤: 强烈建议按照“思考内容”中的“设计步骤指南”来引导创作过程。

目标结构模板:

```yaml
姓名: '[角色全名]'
别名:
  - '[别名1]'
  - '[别名2]'

基本信息:
  性别: '[性别]'
  年龄: '[年龄]'
  生日: '[生日]'
  身高: '[身高]'
  所属机构: '[所属机构]'
  社团与职位:
    - '[组织/社团1]': '[职位1], [简要职责描述，思考如何服务或掩饰核心特质]'
  爱好:
    - '[爱好1]'

外貌:
  眼睛: '[颜色], [形状], [基础眼神状态]。[设计眼神如何微妙地泄露真实情绪或内在冲突?]'
  头发: '[颜色], [长度], [发型], [发质]。[头发或相关动作是否会成为无意识的压力/情绪指示器?]'
  耳朵/角/尾巴等特征: '[非人特征描述，或设为 '无']。[这些特征能否不由自主地反映情绪?]'
  身材: '[体型], [体态], [肤色], [其他特征]。[姿态在特定情绪或防御状态下如何变化?]'
  日常制服/着装:
    描述: |
      [设计一套符合其核心概念、可能具有象征意义的着装]
    心理关联与动态影响: |
      [分析该着装如何服务于其外在形象构建？是强化、掩饰还是束缚其内在特质？]
    其他着装形态:
      - 形态名称: '[形态名称]'
        描述: |
          [描述]
        心理关联与动态影响: |
          [此形态下的心理变化？]

声音:
  音色语调特点: |
    [设计基础音色、语速、语调。][关键：在特定内在状态（如紧张、愤怒、脆弱被触碰）下，声音会发生哪些不易察觉但可感知的变化？]
  常用语:
    - '[常用语1]'

BACKGROUND: (背景故事，采用时间轴分段描述)
    timeline:
      - period: <时间段>
        event: <关键事件>
        impact: <对当前性格的影响>
    turningPoint: <人生转折事件>

人物关系:
  - With_{{Other_Character_Name}}: (与其他角色的关系)
    - publicDynamic: <公开互动模式>
    - privateDynamic: <私下互动模式>

核心特质:
  - 特质1:
  - 特质2:
  - 特质3:

琐事 / 可用细节:
  - '[设计细节1，例如：在说谎或掩饰时，会下意识地轻舔嘴唇]'
  - '[设计细节2，例如：独处时会哼唱一首与其外在形象反差很大的歌曲]'
```