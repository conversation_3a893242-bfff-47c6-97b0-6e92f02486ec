/<options>\s*{option1\|([\s\S]*?)}\s*{option2\|([\s\S]*?)}\s*{option3\|([\s\S]*?)}\s*{option4\|([\s\S]*?)}\s*{option5\|([\s\S]*?)}\s*</options>/g

<options>
    {option1|{{user}}|"你为什么要这么做？"}
    {option2|{{user}}|"我走上前，仔细观察那幅画。"}
    {option3|{{user}}|我什么也不说，只是冷冷地看着他。}
    {option4|[NPC姓名]|[她紧张地绞着手指，避开了{{user}}的目光。]}
    {option5|[另一NPC姓名]|[他突然放声大笑，仿佛听到了什么天大的笑话。]}
</options>

```
<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选项</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 5px;
        }

        .options-container {
            max-width: 1100px;
            margin: 0 auto;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .options-grid {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .option-button {
            background-color: #e9ecef;
            color: #343a40;
            border: 1px solid #ced4da;
            border-radius: 6px;
            padding: 5px 10px; /* Increased left padding for easier text selection */
            text-align: left;
            font-size: 14px;
            transition: all 0.2s ease;
            white-space: normal;
            word-wrap: break-word;
            min-height: 40px;
            display: flex;
            align-items: center;
            line-height: 1.4;
            width: 100%; /* Make button take full width */
            box-sizing: border-box; /* Include padding and border in width */
            user-select: text; /* Allow text selection */
        }

        .option-button:hover {
            background-color: #dee2e6;
            border-color: #adb5bd;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
            transform: translateY(-1px);
        }

        .option-button:active {
            transform: translateY(0);
            background-color: #d1d5db;
        }

        .copied-feedback {
            background-color: #d1e7dd !important;
            color: #0f5132 !important;
            border-color: #badbcc !important;
        }

    </style>
</head>
<body>

<div class="options-container">
    <div class="options-grid">
        <button class="option-button" data-copy-text='$1'>$1</button>
        <button class="option-button" data-copy-text='$2'>$2</button>
        <button class="option-button" data-copy-text='$3'>$3</button>
        <button class="option-button" data-copy-text='$4'>$4</button>
        <button class="option-button" data-copy-text='$5'>$5</button>
</div>

<script>
    function copyToClipboard(buttonElement, textToCopy) {
        navigator.clipboard.writeText(textToCopy).then(() => {
            buttonElement.classList.add('copied-feedback');
            setTimeout(() => {
                if (document.body.contains(buttonElement)) {
                    buttonElement.classList.remove('copied-feedback');
                }
            }, 1000);

        }).catch(err => {
            console.error('无法复制文本: ', err);
        });
    }
    document.addEventListener('DOMContentLoaded', () => {
        const allOptionButtons = document.querySelectorAll('.option-button');
        allOptionButtons.forEach(button => {
            button.addEventListener('click', function() {
                const textToCopy = this.dataset.copyText;
                copyToClipboard(this, textToCopy);
            });
        });
    });
</script>

</body>
</html>
```