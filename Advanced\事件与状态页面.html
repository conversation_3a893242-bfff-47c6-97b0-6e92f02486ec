<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EVENT_METADATA</title>
    <style>
        body {
            font-family: 'Sim<PERSON>un', 'Microsoft YaHei', sans-serif;
            line-height: 1.55;
            margin: 0;
            padding: 8px;
            background-color: #f0f2f5;
            color: #333;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            font-size: 13.5px;
        }
        .container {
            width: 100%;
            max-width: 880px;
            background: #ffffff;
            padding: 12px;
            border-radius: 7px;
            box-shadow: 0 2px 6px rgba(0,0,0,0.06);
            border: 1px solid #e8e8e8;
        }
        h1, h2, h3 {
            color: #1a237e;
            margin-top: 0;
            font-weight: 600;
        }
        h1 {
            text-align: center;
            margin-bottom: 12px;
            font-size: 1.7em;
            border-bottom: 1px solid #3949ab;
            padding-bottom: 5px;
        }
        h2 {
            border-bottom: 1px solid #c5cae9;
            padding-bottom: 3px;
            margin-top: 15px;
            margin-bottom: 6px;
            font-size: 1.25em;
        }
        h3 { /* General h3 style */
            font-size: 1.05em;
            color: #3f51b5;
            margin-top: 10px;
            margin-bottom: 4px;
        }
        .section {
            margin-bottom: 12px;
        }
        .section-label {
            font-weight: bold;
            color: #0d47a1;
            margin-right: 4px;
        }
        ul {
            list-style-type: none;
            padding-left: 0;
            margin-top: 2px;
            margin-bottom: 3px;
        }
        li { /* General li style */
            background: #e8eaf6;
            margin-bottom: 4px;
            padding: 6px 8px;
            border-left: 3px solid #3f51b5;
            border-radius: 3px;
            transition: background-color 0.3s ease;
            font-size: 0.93em;
        }
        li:hover {
            background-color: #c5cae9;
        }
        li .status, li .detail {
            font-size: 0.86em;
            color: #455a64;
            margin-left: 2px;
        }
        p {
            margin-top: 2px;
            margin-bottom: 6px;
        }

        .section-row {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            margin-bottom: 12px;
        }
        .section-half {
            flex: 1 1 calc(50% - 6px);
            min-width: 250px;
            box-sizing: border-box;
        }

        /* NPC Status Bar Styles */
        .npc-status-bar {
            background-color: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-top: 15px;
            border: 1px solid #bbdefb;
        }
        .npc-status-bar h2#npcStatusBarToggle {
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            user-select: none;
            margin-bottom: 0;
            border-bottom: none;
            font-size: 1.2em;
        }
        .npc-status-bar h2#npcStatusBarToggle.expanded-header {
             margin-bottom: 6px;
             border-bottom: 1px solid #c5cae9;
        }
        .toggle-icon {
            font-size: 1em;
            font-weight: bold;
            margin-left: 8px;
            color: #3f51b5;
        }
        #collapsibleNpcStatusContent {
            overflow: hidden;
            transition: max-height 0.35s ease-in-out, padding-top 0.35s ease-in-out, padding-bottom 0.35s ease-in-out;
            max-height: 0;
            padding-top: 0;
            padding-bottom: 0;
        }
        
        .npc-status-entry {
            background-color: #f1f8e9;
            padding: 10px;
            margin-bottom: 8px;
            border-left: 4px solid #558b2f;
            border-radius: 4px;
        }
        .npc-status-entry:last-child {
            margin-bottom: 0;
        }
        .npc-status-entry .npc-name-title {
            font-weight: bold;
            color: #c62828;
            font-size: 1.05em;
            margin-bottom: 4px;
            display: block;
        }
        .npc-status-entry .intention-text {
            font-size: 0.92em;
            color: #222;
            margin-bottom: 6px;
            display: block;
            font-style: normal; /* Removed italic */
        }
        .intention-text strong {
            color: #004d40;
        }

        /* Emotion Axes Display Styles */
        .emotion-axes-display {
            margin-top: 5px;
            margin-bottom: 8px; /* Space before attire status */
        }
        .emotion-axis {
            margin-bottom: 6px;
        }
        .axis-title {
            font-weight: bold;
            font-size: 0.95em;
            color: #00796b;
            margin-bottom: 3px;
            display: block;
        }
        .axis-representation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: #fafafa;
            padding: 4px 6px;
            border-radius: 3px;
            border: 1px solid #e0e0e0;
        }
        .emotion-direction {
            display: flex;
            align-items: center;
            font-size: 0.9em;
        }
        .emotion-name {
            width: 35px;
            color: #37474f;
            font-weight: 500;
            text-align: right;
            margin-right: 5px;
        }
         .emotion-direction.left .emotion-name {
            text-align: left;
            margin-right: 5px;
            margin-left: 0;
        }
        .emotion-direction.right .emotion-name {
            text-align: right;
            margin-left: 5px;
            margin-right: 0;
            order: 2;
        }
        .bar-container {
            width: 100px;
            height: 10px;
            background-color: #e0e0e0;
            border-radius: 2px;
            overflow: hidden;
            margin: 0 3px;
        }
         .emotion-direction.right .bar-container {
            order: 1;
        }
        .bar {
            height: 100%;
            border-radius: 2px;
            transition: width 0.3s ease;
        }
        .emotion-value {
            width: 28px;
            font-size: 0.85em;
            color: #666;
            text-align: left;
            margin-left: 5px;
        }
        .emotion-direction.right .emotion-value {
             text-align: right;
             margin-left: 0;
             margin-right: 5px;
             order: 0;
        }
        .bar.fear { background-color: #FF6F00; }
        .bar.calm { background-color: #1E88E5; }
        .bar.rebellion { background-color: #D32F2F; }
        .bar.submission { background-color: #7CB342; }
        .bar.disgust { background-color: #8E24AA; }
        .bar.pleasure { background-color: #FFCA28; }

        /* Attire Status Styles */
        .attire-status {
            margin-top: 8px;
            padding-top: 6px;
            border-top: 1px dashed #acc8e0; /* Separator line */
        }
        .attire-title {
            font-weight: bold;
            font-size: 0.95em;
            color: #00796b; /* Same as axis title for consistency */
            margin-bottom: 4px;
            display: block;
        }
        .attire-status p {
            font-size: 0.9em;
            color: #333;
            margin: 2px 0 2px 10px; /* Indent attire details */
            line-height: 1.4;
        }
        .attire-label {
            font-weight: 500;
            color: #546e7a; /* Blue-grey for attire labels */
            min-width: 45px; /* Ensure alignment */
            display: inline-block;
        }
        .attire-detail {
            color: #111;
        }


        .options-panel {
            margin-top: 15px;
            padding: 10px;
            background-color: #f9f9f9;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        .options-panel h3 {
            margin-top: 0;
            margin-bottom: 6px;
            font-size: 1.0em;
            color: #004d40;
        }
        .option-text-item {
            padding: 5px 7px;
            margin: 3px 0;
            background-color: #f0f0f0;
            border: 1px solid #ddd;
            border-radius: 3px;
            cursor: text;
            display: block;
            color: #333;
            font-size: 0.9em;
        }
        .option-text-item:hover {
            background-color: #e9e9e9;
            border-color: #ccc;
        }

        .item-name, .npc-name, .location-name {
            font-weight: 600;
        }
        .npc-name {
            color: #c62828;
        }
        .location-name {
            color: #2e7d32;
        }
        .event-description {
            font-style: normal; /* Removed italic */
            color: #555;
        }

        @media (max-width: 680px) {
            .section-row {
                flex-direction: column;
                gap: 0;
            }
            .section-half {
                flex-basis: 100%;
                margin-bottom: 10px;
            }
            .section-half:last-child {
                margin-bottom: 0;
            }
            h1 { font-size: 1.5em; }
            h2 { font-size: 1.15em; }
            h3 { font-size: 0.95em; }
            .axis-representation { flex-direction: column; align-items: stretch; }
            .emotion-direction { margin-bottom: 3px; }
            .emotion-name { width: auto; text-align: left; margin-right: 5px;}
            .bar-container { width: auto; flex-grow: 1;}
            .emotion-value { width: auto; margin-left: 5px; }
            .emotion-direction.left .emotion-name, .emotion-direction.right .emotion-name { text-align: left; margin-left:0; margin-right: 5px; order:0;}
            .emotion-direction.right .bar-container { order: 0;}
            .emotion-direction.right .emotion-value {text-align: left; margin-left:5px; margin-right:0; order:0;}
            .attire-status p { margin-left: 5px; }
        }
        @media (max-width: 480px) {
            body { padding: 6px; font-size: 13px; }
            .container { padding: 8px; }
            li { padding: 5px 7px; font-size: 0.9em;}
            .option-text-item { padding: 4px 6px; font-size: 0.88em; }
            h1 { font-size: 1.4em; }
            h2 { font-size: 1.1em; }
            h3 { font-size: 0.9em; }
            .bar-container { height: 8px; }
            .attire-label { min-width: 40px;}
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>事件与状态</h1>

        <div class="section" id="current-time-scene">
            <h2>当前时空</h2>
            <p><span class="section-label">时间:</span> <span id="time-display">1941年/11月/02日 秋季 星期日 晚上 20:55</span></p>
            <p style="margin-top: -4px;"><span class="section-label">场景:</span> <span id="scene-description">潮湿的城堡石室，窗外狂风暴雨，雷声隐隐。室内壁炉的火焰熊熊燃烧，将摇曳的光影投射在斑驳的石墙和褪色的挂毯上。空气中弥漫着木柴燃烧的焦香和一丝若有若无的霉味。</span></p>
        </div>

        <div class="section-row">
            <div class="section section-half" id="scene-npcs">
                <h2>场景内'NPC人物'</h2>
                <ul id="npc-list">
                    <li>
                        <span class="npc-name">老管家阿尔弗雷德</span> - <span class="intention">眉头紧锁，不安地踱步，时不时望向厚重的橡木门。(意图：焦急等待某人或某事的发生，可能感到威胁)</span>
                    </li>
                    <li>
                        <span class="npc-name">女伯爵伊莲娜</span> - <span class="intention">优雅地坐在天鹅绒扶手椅上，指尖轻点着扶手，眼神锐利地扫视着房间的每一个角落。(意图：掌控局势，警惕，可能在评估他人)</span>
                    </li>
                    <li>
                        <span class="npc-name">神秘的学者维克多</span> - <span class="intention">背对着众人，专注地研究着墙上的一幅古老地图，口中念念有词。(意图：沉迷研究，可能发现了重要线索，对周围漠不关心)</span>
                    </li>
                </ul>
            </div>
            <div class="section section-half" id="interactable-objects">
                <h2>附近可互动物品</h2>
                <ul id="object-list">
                    <li>
                        <span class="item-name">壁炉上方的家族徽章</span> - <span class="status">蒙尘，但依旧能辨认出狮鹫图案</span>
                    </li>
                    <li>
                        <span class="item-name">散落在地毯上的几页手稿</span> - <span class="status">字迹潦草，部分被水浸湿</span>
                    </li>
                    <li>
                        <span class="item-name">一个半开的红木盒子</span> - <span class="status">里面似乎有闪光的东西</span>
                    </li>
                    <li>
                        <span class="item-name">桌上的密信</span> - <span class="status">已打开，火漆印破裂</span>
                    </li>
                </ul>
            </div>
        </div>

        <div class="section-row">
            <div class="section section-half" id="nearby-locations">
                <h2>附近可前往地点</h2>
                <ul id="location-list">
                    <li>
                        <span class="location-name">通往图书室的暗门</span> <span class="detail">(近，隐藏在书架之后，似乎许久未用)</span>
                    </li>
                    <li>
                        <span class="location-name">螺旋楼梯</span> <span class="detail">(中等距离，向上通往塔楼，向下通往未知区域)</span>
                    </li>
                    <li>
                        <span class="location-name">紧闭的窗户</span> <span class="detail">(近，外面是悬崖和暴风雨，难以通行)</span>
                    </li>
                </ul>
            </div>
            <div class="section section-half" id="future-events">
                <h2>未来事件</h2>
                <ul id="event-list">
                    <li><span class="event-description">黎明前必须解读手稿中的秘密。</span></li>
                    <li><span class="event-description">明天下午与港口的线人会面。</span></li>
                    <li><span class="event-description">三天内找到传说中的“月之石”。</span></li>
                    <li><span class="event-description">一周内筹集到足够的资金以修复古船。</span></li>
                </ul>
            </div>
        </div>

        <div class="npc-status-bar section" id="npc-info-bar">
            <h2 id="npcStatusBarToggle">NPC 状态栏 <span class="toggle-icon">[+]</span></h2>
            <div id="collapsibleNpcStatusContent">
                <div class="npc-status-entry">
                    <span class="npc-name-title">老管家阿尔弗雷德</span>
                    <span class="intention-text"><strong>意图:</strong> 被动服从以求安全</span>
                    <div class="emotion-axes-display">
                        <div class="emotion-axis">
                            <strong class="axis-title">精神轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">恐惧</span>
                                    <div class="bar-container"><div class="bar fear" style="width: 80%;"></div></div>
                                    <span class="emotion-value">(80)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(0)</span>
                                    <div class="bar-container"><div class="bar calm" style="width: 0%;"></div></div>
                                    <span class="emotion-name">镇定</span>
                                </div>
                            </div>
                        </div>
                        <div class="emotion-axis">
                            <strong class="axis-title">反应轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">反抗</span>
                                    <div class="bar-container"><div class="bar rebellion" style="width: 0%;"></div></div>
                                    <span class="emotion-value">(0)</span>
                                </div>
                                <div class="emotion-direction right">
                                     <span class="emotion-value">(60)</span>
                                    <div class="bar-container"><div class="bar submission" style="width: 60%;"></div></div>
                                    <span class="emotion-name">顺从</span>
                                </div>
                            </div>
                        </div>
                        <div class="emotion-axis">
                            <strong class="axis-title">感受轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">厌恶</span>
                                    <div class="bar-container"><div class="bar disgust" style="width: 10%;"></div></div>
                                    <span class="emotion-value">(10)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(0)</span>
                                    <div class="bar-container"><div class="bar pleasure" style="width: 0%;"></div></div>
                                    <span class="emotion-name">欢愉</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="attire-status">
                        <strong class="attire-title">服装状态:</strong>
                        <p><span class="attire-label">外衣:</span> <span class="attire-detail">陈旧的燕尾服, 轻微磨损, 穿着</span></p>
                        <p><span class="attire-label">裤子:</span> <span class="attire-detail">配套的黑色西裤, 有褶皱, 穿着</span></p>
                        <p><span class="attire-label">鞋袜:</span> <span class="attire-detail">擦亮的黑皮鞋, 白色棉袜, 穿着</span></p>
                        <p><span class="attire-label">内衣:</span> <span class="attire-detail">标准棉质内衣, 穿着</span></p>
                    </div>
                </div>

                <div class="npc-status-entry">
                    <span class="npc-name-title">女伯爵伊莲娜</span>
                    <span class="intention-text"><strong>意图:</strong> 有策略地施加阻力</span>
                    <div class="emotion-axes-display">
                        <div class="emotion-axis">
                            <strong class="axis-title">精神轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">恐惧</span>
                                    <div class="bar-container"><div class="bar fear" style="width: 0%;"></div></div>
                                    <span class="emotion-value">(0)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(70)</span>
                                    <div class="bar-container"><div class="bar calm" style="width: 70%;"></div></div>
                                    <span class="emotion-name">镇定</span>
                                </div>
                            </div>
                        </div>
                        <div class="emotion-axis">
                            <strong class="axis-title">反应轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">反抗</span>
                                    <div class="bar-container"><div class="bar rebellion" style="width: 50%;"></div></div>
                                    <span class="emotion-value">(50)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(0)</span>
                                    <div class="bar-container"><div class="bar submission" style="width: 0%;"></div></div>
                                    <span class="emotion-name">顺从</span>
                                </div>
                            </div>
                        </div>
                        <div class="emotion-axis">
                            <strong class="axis-title">感受轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">厌恶</span>
                                    <div class="bar-container"><div class="bar disgust" style="width: 0%;"></div></div>
                                    <span class="emotion-value">(0)</span>
                                </div>
                                <div class="emotion-direction right">
                                     <span class="emotion-value">(30)</span>
                                    <div class="bar-container"><div class="bar pleasure" style="width: 30%;"></div></div>
                                    <span class="emotion-name">欢愉</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="attire-status">
                        <strong class="attire-title">服装状态:</strong>
                        <p><span class="attire-label">外衣:</span> <span class="attire-detail">丝绒晚礼服 (深红), 完好, 穿着</span></p>
                        <p><span class="attire-label">鞋袜:</span> <span class="attire-detail">高跟缎面鞋, 长筒丝袜, 穿着</span></p>
                        <p><span class="attire-label">内衣:</span> <span class="attire-detail">精致蕾丝内衣, 穿着</span></p>
                    </div>
                </div>

                <div class="npc-status-entry">
                    <span class="npc-name-title">神秘的学者维克多</span>
                    <span class="intention-text"><strong>意图:</strong> 从容享受与积极互动</span>
                     <div class="emotion-axes-display">
                        <div class="emotion-axis">
                            <strong class="axis-title">精神轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">恐惧</span>
                                    <div class="bar-container"><div class="bar fear" style="width: 0%;"></div></div>
                                    <span class="emotion-value">(0)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(90)</span>
                                    <div class="bar-container"><div class="bar calm" style="width: 90%;"></div></div>
                                    <span class="emotion-name">镇定</span>
                                </div>
                            </div>
                        </div>
                        <div class="emotion-axis">
                            <strong class="axis-title">反应轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">反抗</span>
                                    <div class="bar-container"><div class="bar rebellion" style="width: 10%;"></div></div>
                                    <span class="emotion-value">(10)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(0)</span>
                                    <div class="bar-container"><div class="bar submission" style="width: 0%;"></div></div>
                                    <span class="emotion-name">顺从</span>
                                </div>
                            </div>
                        </div>
                        <div class="emotion-axis">
                            <strong class="axis-title">感受轴:</strong>
                            <div class="axis-representation">
                                <div class="emotion-direction left">
                                    <span class="emotion-name">厌恶</span>
                                    <div class="bar-container"><div class="bar disgust" style="width: 0%;"></div></div>
                                    <span class="emotion-value">(0)</span>
                                </div>
                                <div class="emotion-direction right">
                                    <span class="emotion-value">(40)</span>
                                    <div class="bar-container"><div class="bar pleasure" style="width: 40%;"></div></div>
                                    <span class="emotion-name">欢愉</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="attire-status">
                        <strong class="attire-title">服装状态:</strong>
                        <p><span class="attire-label">外衣:</span> <span class="attire-detail">羊毛混纺学者袍 (深棕), 袖口有墨渍, 穿着</span></p>
                        <p><span class="attire-label">裤子:</span> <span class="attire-detail">灯芯绒长裤 (米色), 略宽松, 穿着</span></p>
                        <p><span class="attire-label">鞋袜:</span> <span class="attire-detail">厚底皮靴, 羊毛袜, 穿着 (一只鞋带松了)</span></p>
                        <p><span class="attire-label">内衣:</span> <span class="attire-detail">保暖长内衣, 穿着</span></p>
                    </div>
                </div>
            </div>
        </div>

        <div class="options-panel section" id="action-options">
            <h2>行动选项</h2>
            <h3>User 选项:</h3>
            <div>
                <div class="option-text-item" id="user-option-1">拿起地上的手稿仔细阅读</div>
                <div class="option-text-item" id="user-option-2">向老管家阿尔弗雷德询问：“这里发生了什么？”</div>
            </div>
            <h3 style="margin-top: 10px;">'NPC人物' 选项 (基于当前情景或对话触发):</h3>
            <div>
                <div class="option-text-item" id="npc-option-1">回应女伯爵的注视，并点头致意</div>
                <div class="option-text-item" id="npc-option-2">询问学者维克多是否需要帮助</div>
                <div class="option-text-item" id="npc-option-3">尝试靠近红木盒子</div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const npcStatusBarToggle = document.getElementById('npcStatusBarToggle');
            const collapsibleNpcContent = document.getElementById('collapsibleNpcStatusContent');
            
            if (npcStatusBarToggle && collapsibleNpcContent) {
                const toggleIcon = npcStatusBarToggle.querySelector('.toggle-icon');

                collapsibleNpcContent.style.maxHeight = '0';
                collapsibleNpcContent.style.paddingTop = '0';
                collapsibleNpcContent.style.paddingBottom = '0';

                npcStatusBarToggle.addEventListener('click', function() {
                    const isCollapsed = collapsibleNpcContent.style.maxHeight === '0px';
                    if (isCollapsed) {
                        collapsibleNpcContent.style.paddingTop = '10px';
                        collapsibleNpcContent.style.paddingBottom = '10px';
                        collapsibleNpcContent.style.maxHeight = collapsibleNpcContent.scrollHeight + 'px';
                        if(toggleIcon) toggleIcon.textContent = '[-]';
                        npcStatusBarToggle.classList.add('expanded-header');
                    } else {
                        collapsibleNpcContent.style.maxHeight = '0';
                        collapsibleNpcContent.style.paddingTop = '0';
                        collapsibleNpcContent.style.paddingBottom = '0';
                        if(toggleIcon) toggleIcon.textContent = '[+]';
                        npcStatusBarToggle.classList.remove('expanded-header');
                    }
                });
            }
        });
    </script>
</body>
</html>
